/**
 * Subscription-Aware Caching System
 * 
 * Implements intelligent caching for subscription validation with automatic invalidation
 * based on subscription expiry dates and performance optimization for repeated checks.
 * 
 * Created: 2025-01-07
 * Part of: Phase 1 - Foundation & API Layer
 */

import {
  SubscriptionStatus,
  SubscriptionValidationResult,
  CacheConfig,
  CacheOperationResult,
  DEFAULT_CACHE_CONFIG,
  MIN_CACHE_TTL,
} from './types';
import { validateUserSubscription, hasActiveSubscription } from './validation';

// =========================
// Cache Storage Interface
// =========================

interface CacheEntry {
  data: SubscriptionStatus;
  timestamp: number;
  expiresAt: number;
  subscriptionExpiry: string | null;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalRequests: number;
  hitRate: number;
  averageResponseTime: number;
}

// =========================
// In-Memory Cache Implementation
// =========================

class SubscriptionCache {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
    hitRate: 0,
    averageResponseTime: 0,
  };

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config };
    
    // Start cleanup interval
    this.startCleanupInterval();
    
    console.log('[Cache] Subscription cache initialized with config:', this.config);
  }

  /**
   * Get subscription status from cache
   */
  async get(userId: string): Promise<{ data: SubscriptionStatus | null; result: CacheOperationResult }> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      const key = this.generateCacheKey(userId);
      const entry = this.cache.get(key);

      if (!entry) {
        this.stats.misses++;
        this.updateStats(startTime);
        return { data: null, result: 'miss' };
      }

      const now = Date.now();

      // Check if entry is expired
      if (entry.expiresAt <= now) {
        this.cache.delete(key);
        this.stats.misses++;
        this.updateStats(startTime);
        return { data: null, result: 'expired' };
      }

      // Check if subscription has expired (even if cache entry hasn't)
      if (entry.subscriptionExpiry) {
        const subscriptionExpiry = new Date(entry.subscriptionExpiry).getTime();
        if (subscriptionExpiry <= now) {
          // Subscription expired - invalidate cache entry
          this.cache.delete(key);
          this.stats.misses++;
          this.updateStats(startTime);
          return { data: null, result: 'expired' };
        }
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = now;

      this.stats.hits++;
      this.updateStats(startTime);

      console.log(`[Cache] Hit for user ${userId} (accessed ${entry.accessCount} times)`);
      return { data: entry.data, result: 'hit' };

    } catch (error) {
      console.error('[Cache] Error retrieving from cache:', error);
      this.stats.misses++;
      this.updateStats(startTime);
      return { data: null, result: 'error' };
    }
  }

  /**
   * Set subscription status in cache with intelligent TTL
   */
  async set(userId: string, data: SubscriptionStatus): Promise<CacheOperationResult> {
    try {
      const key = this.generateCacheKey(userId, data.subscriptionExpiry);
      const now = Date.now();
      
      // Calculate intelligent TTL based on subscription expiry
      const ttl = this.calculateIntelligentTTL(data);
      const expiresAt = now + (ttl * 1000);

      const entry: CacheEntry = {
        data,
        timestamp: now,
        expiresAt,
        subscriptionExpiry: data.subscriptionExpiry,
        accessCount: 0,
        lastAccessed: now,
      };

      // Ensure we don't exceed max entries
      if (this.cache.size >= this.config.maxEntries) {
        this.evictLeastRecentlyUsed();
      }

      this.cache.set(key, entry);

      console.log(`[Cache] Set for user ${userId} with TTL ${ttl}s (expires: ${new Date(expiresAt).toISOString()})`);
      return 'hit';

    } catch (error) {
      console.error('[Cache] Error setting cache:', error);
      return 'error';
    }
  }

  /**
   * Invalidate cache entry for a specific user
   */
  async invalidate(userId: string): Promise<boolean> {
    try {
      const key = this.generateCacheKey(userId);
      const deleted = this.cache.delete(key);
      
      if (deleted) {
        console.log(`[Cache] Invalidated cache for user ${userId}`);
      }
      
      return deleted;
    } catch (error) {
      console.error('[Cache] Error invalidating cache:', error);
      return false;
    }
  }

  /**
   * Invalidate all cache entries for users with expired subscriptions
   */
  async invalidateExpired(): Promise<number> {
    let invalidatedCount = 0;
    const now = Date.now();

    try {
      for (const [key, entry] of this.cache.entries()) {
        let shouldInvalidate = false;

        // Check cache expiry
        if (entry.expiresAt <= now) {
          shouldInvalidate = true;
        }

        // Check subscription expiry
        if (entry.subscriptionExpiry) {
          const subscriptionExpiry = new Date(entry.subscriptionExpiry).getTime();
          if (subscriptionExpiry <= now) {
            shouldInvalidate = true;
          }
        }

        if (shouldInvalidate) {
          this.cache.delete(key);
          invalidatedCount++;
        }
      }

      if (invalidatedCount > 0) {
        console.log(`[Cache] Invalidated ${invalidatedCount} expired entries`);
      }

      return invalidatedCount;
    } catch (error) {
      console.error('[Cache] Error invalidating expired entries:', error);
      return 0;
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`[Cache] Cleared all ${size} cache entries`);
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache size and memory usage info
   */
  getInfo(): { size: number; maxEntries: number; memoryUsage: string } {
    const size = this.cache.size;
    const memoryUsage = `${Math.round((size * 1024) / 1024 * 100) / 100} MB (estimated)`;
    
    return {
      size,
      maxEntries: this.config.maxEntries,
      memoryUsage,
    };
  }

  // =========================
  // Private Helper Methods
  // =========================

  private generateCacheKey(userId: string, subscriptionExpiry?: string | null): string {
    let key = `${this.config.keyPrefix}${userId}`;
    
    if (subscriptionExpiry) {
      // Include subscription expiry in key to ensure automatic invalidation
      const expiryHash = this.hashString(subscriptionExpiry);
      key += `:${expiryHash}`;
    }
    
    return key;
  }

  private calculateIntelligentTTL(data: SubscriptionStatus): number {
    const baseTTL = this.config.ttl;
    
    // If subscription has expiry date, don't cache beyond that
    if (data.subscriptionExpiry) {
      const subscriptionExpiry = new Date(data.subscriptionExpiry).getTime();
      const now = Date.now();
      const timeUntilExpiry = Math.floor((subscriptionExpiry - now) / 1000);
      
      if (timeUntilExpiry > 0 && timeUntilExpiry < baseTTL) {
        // Cache until subscription expires, but not less than minimum TTL
        return Math.max(timeUntilExpiry, MIN_CACHE_TTL);
      }
    }
    
    // For users without active subscriptions, use shorter TTL
    if (!data.hasActiveSubscription) {
      return Math.max(baseTTL / 2, MIN_CACHE_TTL);
    }
    
    return baseTTL;
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      console.log(`[Cache] Evicted LRU entry: ${oldestKey}`);
    }
  }

  private startCleanupInterval(): void {
    // Run cleanup every 5 minutes
    setInterval(() => {
      this.invalidateExpired();
    }, 5 * 60 * 1000);
  }

  private updateStats(startTime: number): void {
    const responseTime = Date.now() - startTime;
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
    this.stats.hitRate = this.stats.hits / this.stats.totalRequests;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

// =========================
// Global Cache Instance
// =========================

const subscriptionCache = new SubscriptionCache();

// =========================
// Public Cache API
// =========================

/**
 * Get subscription status with caching
 */
export async function getCachedSubscriptionStatus(
  userId: string,
  forceRefresh: boolean = false
): Promise<SubscriptionStatus> {
  const startTime = Date.now();

  try {
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const { data: cachedData, result } = await subscriptionCache.get(userId);
      
      if (cachedData && result === 'hit') {
        console.log(`[Cache API] Cache hit for user ${userId} (${Date.now() - startTime}ms)`);
        return {
          ...cachedData,
          validationSource: 'cache',
          lastValidated: cachedData.lastValidated,
        };
      }
    }

    // Cache miss or force refresh - validate from database
    console.log(`[Cache API] Cache miss for user ${userId}, validating from database`);
    const validationResult = await validateUserSubscription(userId, {
      useCache: false,
      includeMetrics: true,
    });

    // Cache the result if validation was successful
    if (validationResult.success) {
      await subscriptionCache.set(userId, validationResult.status);
    }

    console.log(`[Cache API] Database validation completed for user ${userId} (${Date.now() - startTime}ms)`);
    return validationResult.status;

  } catch (error) {
    console.error('[Cache API] Error getting cached subscription status:', error);

    // Fail secure - return basic member status
    return {
      hasActiveSubscription: false,
      currentTier: 'MEMBER',
      subscriptionExpiry: null,
      isValid: false,
      lastValidated: new Date().toISOString(),
      validationSource: 'fallback',
      warnings: ['Cache error - defaulting to MEMBER tier for security'],
    };
  }
}

/**
 * Quick cached check for active subscription (optimized for performance)
 */
export async function hasCachedActiveSubscription(userId: string): Promise<boolean> {
  try {
    const { data: cachedData } = await subscriptionCache.get(userId);

    if (cachedData) {
      return cachedData.hasActiveSubscription;
    }

    // Cache miss - use quick database check
    const hasActive = await hasActiveSubscription(userId);

    // Don't cache this lightweight check to avoid cache pollution
    return hasActive;

  } catch (error) {
    console.error('[Cache API] Error checking cached active subscription:', error);
    return false; // Fail secure
  }
}

/**
 * Warm cache for frequently accessed users
 */
export async function warmSubscriptionCache(userIds: string[]): Promise<{
  warmed: number;
  failed: number;
  duration: number;
}> {
  const startTime = Date.now();
  let warmed = 0;
  let failed = 0;

  console.log(`[Cache Warming] Starting cache warming for ${userIds.length} users`);

  try {
    // Process in batches to avoid overwhelming the system
    const batchSize = 5;

    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);

      const batchPromises = batch.map(async (userId) => {
        try {
          // Check if already cached
          const { result } = await subscriptionCache.get(userId);

          if (result === 'hit') {
            console.log(`[Cache Warming] User ${userId} already cached, skipping`);
            return;
          }

          // Validate and cache
          const validationResult = await validateUserSubscription(userId);

          if (validationResult.success) {
            await subscriptionCache.set(userId, validationResult.status);
            warmed++;
            console.log(`[Cache Warming] Warmed cache for user ${userId}`);
          } else {
            failed++;
            console.warn(`[Cache Warming] Failed to validate user ${userId}`);
          }
        } catch (error) {
          failed++;
          console.error(`[Cache Warming] Error warming cache for user ${userId}:`, error);
        }
      });

      await Promise.all(batchPromises);

      // Small delay between batches
      if (i + batchSize < userIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const duration = Date.now() - startTime;
    console.log(`[Cache Warming] Completed: ${warmed} warmed, ${failed} failed in ${duration}ms`);

    return { warmed, failed, duration };

  } catch (error) {
    console.error('[Cache Warming] Error during cache warming:', error);
    return { warmed, failed, duration: Date.now() - startTime };
  }
}

/**
 * Invalidate cache for specific user
 */
export async function invalidateUserCache(userId: string): Promise<boolean> {
  try {
    const result = await subscriptionCache.invalidate(userId);
    console.log(`[Cache API] Invalidated cache for user ${userId}: ${result}`);
    return result;
  } catch (error) {
    console.error('[Cache API] Error invalidating user cache:', error);
    return false;
  }
}

/**
 * Invalidate cache for multiple users
 */
export async function invalidateMultipleUserCache(userIds: string[]): Promise<{
  invalidated: number;
  failed: number;
}> {
  let invalidated = 0;
  let failed = 0;

  try {
    for (const userId of userIds) {
      const result = await subscriptionCache.invalidate(userId);
      if (result) {
        invalidated++;
      } else {
        failed++;
      }
    }

    console.log(`[Cache API] Batch invalidation: ${invalidated} invalidated, ${failed} failed`);
    return { invalidated, failed };

  } catch (error) {
    console.error('[Cache API] Error during batch invalidation:', error);
    return { invalidated, failed: userIds.length };
  }
}

/**
 * Get cache statistics and performance metrics
 */
export function getCacheStats(): CacheStats & { info: ReturnType<typeof subscriptionCache.getInfo> } {
  return {
    ...subscriptionCache.getStats(),
    info: subscriptionCache.getInfo(),
  };
}

/**
 * Clear all cached subscription data
 */
export async function clearSubscriptionCache(): Promise<void> {
  try {
    await subscriptionCache.clear();
    console.log('[Cache API] All subscription cache cleared');
  } catch (error) {
    console.error('[Cache API] Error clearing cache:', error);
  }
}

/**
 * Perform cache maintenance (cleanup expired entries)
 */
export async function performCacheMaintenance(): Promise<{
  expiredRemoved: number;
  totalEntries: number;
  hitRate: number;
}> {
  try {
    const expiredRemoved = await subscriptionCache.invalidateExpired();
    const stats = subscriptionCache.getStats();
    const info = subscriptionCache.getInfo();

    console.log(`[Cache Maintenance] Removed ${expiredRemoved} expired entries, ${info.size} total entries remaining`);

    return {
      expiredRemoved,
      totalEntries: info.size,
      hitRate: stats.hitRate,
    };

  } catch (error) {
    console.error('[Cache Maintenance] Error during maintenance:', error);
    return {
      expiredRemoved: 0,
      totalEntries: 0,
      hitRate: 0,
    };
  }
}

// =========================
// Cache Configuration Management
// =========================

/**
 * Update cache configuration at runtime
 */
export function updateCacheConfig(newConfig: Partial<CacheConfig>): void {
  // Note: This would require refactoring the cache class to support runtime config updates
  console.warn('[Cache API] Runtime config updates not yet implemented. Restart required for config changes.');
}

/**
 * Export cache instance for advanced usage
 */
export { subscriptionCache };
